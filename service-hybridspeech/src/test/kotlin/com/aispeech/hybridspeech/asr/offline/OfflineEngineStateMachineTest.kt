package com.aispeech.hybridspeech.asr.offline

import org.junit.Test
import org.junit.Assert.*

/**
 * OfflineEngine 状态机重构测试
 * 验证状态机是否正确替换了原有的状态变量
 */
class OfflineEngineStateMachineTest {

    @Test
    fun testInitialState() {
        val stateMachine = OfflineEngineStateMachine()
        
        // 初始状态应该是 Uninitialized
        assertTrue("Initial state should be Uninitialized", stateMachine.isUninitialized())
        assertFalse("Should not be initializing", stateMachine.isInitializing())
        assertFalse("Should not be initialized", stateMachine.isInitialized())
        assertFalse("Should not be running", stateMachine.isRunning())
        assertFalse("Should not be paused", stateMachine.isPaused())
        assertFalse("Should not be caching", stateMachine.isCaching())
        assertEquals("Cache size should be 0", 0, stateMachine.getCacheSize())
    }

    @Test
    fun testInitializationFlow() {
        val stateMachine = OfflineEngineStateMachine()
        
        // 开始初始化
        assertTrue("Should start initialization", 
            stateMachine.handleEvent(OfflineEngineStateMachine.StateEvent.StartInitialization))
        assertTrue("Should be initializing", stateMachine.isInitializing())
        assertFalse("Should not be initialized yet", stateMachine.isInitialized())
        
        // 开始缓存
        assertTrue("Should start caching", 
            stateMachine.handleEvent(OfflineEngineStateMachine.StateEvent.StartCaching))
        assertTrue("Should be caching", stateMachine.isCaching())
        
        // 添加缓存数据
        assertTrue("Should cache data", 
            stateMachine.handleEvent(OfflineEngineStateMachine.StateEvent.CacheDataReceived(640)))
        assertEquals("Cache size should be 640", 640, stateMachine.getCacheSize())
        
        // 初始化成功
        assertTrue("Should complete initialization", 
            stateMachine.handleEvent(OfflineEngineStateMachine.StateEvent.InitializationSuccess))
        assertTrue("Should be initialized", stateMachine.isInitialized())
        assertFalse("Should not be initializing", stateMachine.isInitializing())
    }

    @Test
    fun testRunningFlow() {
        val stateMachine = OfflineEngineStateMachine()
        
        // 先初始化
        stateMachine.handleEvent(OfflineEngineStateMachine.StateEvent.StartInitialization)
        stateMachine.handleEvent(OfflineEngineStateMachine.StateEvent.InitializationSuccess)
        
        // 启动引擎
        assertTrue("Should start engine", 
            stateMachine.handleEvent(OfflineEngineStateMachine.StateEvent.StartEngine))
        assertTrue("Should be running", stateMachine.isRunning())
        assertFalse("Should not be paused", stateMachine.isPaused())
        
        // 暂停引擎
        assertTrue("Should pause engine", 
            stateMachine.handleEvent(OfflineEngineStateMachine.StateEvent.PauseEngine))
        assertTrue("Should still be running", stateMachine.isRunning())
        assertTrue("Should be paused", stateMachine.isPaused())
        
        // 恢复引擎
        assertTrue("Should resume engine", 
            stateMachine.handleEvent(OfflineEngineStateMachine.StateEvent.ResumeEngine))
        assertTrue("Should be running", stateMachine.isRunning())
        assertFalse("Should not be paused", stateMachine.isPaused())
        
        // 停止引擎
        assertTrue("Should stop engine", 
            stateMachine.handleEvent(OfflineEngineStateMachine.StateEvent.StopEngine))
        assertFalse("Should not be running", stateMachine.isRunning())
        assertTrue("Should be stopped", stateMachine.isStopped())
    }

    @Test
    fun testErrorHandling() {
        val stateMachine = OfflineEngineStateMachine()
        
        // 开始初始化
        stateMachine.handleEvent(OfflineEngineStateMachine.StateEvent.StartInitialization)
        
        // 初始化失败
        assertTrue("Should handle initialization failure", 
            stateMachine.handleEvent(OfflineEngineStateMachine.StateEvent.InitializationFailed("Test error")))
        assertTrue("Should be in error state", stateMachine.isError())
        assertFalse("Should not be initializing", stateMachine.isInitializing())
    }

    @Test
    fun testInvalidTransitions() {
        val stateMachine = OfflineEngineStateMachine()
        
        // 尝试在未初始化状态下启动引擎
        assertFalse("Should not start engine when uninitialized", 
            stateMachine.handleEvent(OfflineEngineStateMachine.StateEvent.StartEngine))
        
        // 尝试在未运行状态下暂停
        assertFalse("Should not pause when not running", 
            stateMachine.handleEvent(OfflineEngineStateMachine.StateEvent.PauseEngine))
    }

    @Test
    fun testStateChangeListener() {
        val stateMachine = OfflineEngineStateMachine()
        var stateChangeCount = 0
        var lastOldState: OfflineEngineStateMachine.EngineState? = null
        var lastNewState: OfflineEngineStateMachine.EngineState? = null
        
        stateMachine.setStateChangeListener { oldState, newState ->
            stateChangeCount++
            lastOldState = oldState
            lastNewState = newState
        }
        
        // 触发状态变化
        stateMachine.handleEvent(OfflineEngineStateMachine.StateEvent.StartInitialization)
        
        assertEquals("Should have one state change", 1, stateChangeCount)
        assertTrue("Old state should be Uninitialized", 
            lastOldState is OfflineEngineStateMachine.EngineState.Uninitialized)
        assertTrue("New state should be Initializing", 
            lastNewState is OfflineEngineStateMachine.EngineState.Initializing)
    }

    @Test
    fun testCacheChannel() {
        val stateMachine = OfflineEngineStateMachine()
        val cacheChannel = stateMachine.getInitCacheChannel()
        
        assertNotNull("Cache channel should not be null", cacheChannel)
        
        // 测试缓存通道容量
        val testData = ByteArray(640) { it.toByte() }
        assertTrue("Should be able to send data to cache channel", 
            cacheChannel.trySend(testData).isSuccess)
    }
}
