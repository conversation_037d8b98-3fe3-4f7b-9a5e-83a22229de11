package com.aispeech.hybridspeech.asr.offline

import android.content.Context
import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.TranscriptionResult
import com.aispeech.hybridspeech.UnifiedJson
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch

/**
 * 离线转写协调器
 * 协调PCM数据流和离线转写引擎
 */
class OfflineOrchestrator(private val context: Context) : CoroutineScope {

  companion object {
    private const val TAG = "OfflineOrchestrator"
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  private val offlineEngine = OfflineEngine(context)

  // 初始化状态跟踪
  private var isEngineInitialized = false
  private var initializationInProgress = false

  private val _transcriptionResultFlow = MutableSharedFlow<TranscriptionResult>(
    replay = 0,
    extraBufferCapacity = 100
  )
  val transcriptionResultFlow: SharedFlow<TranscriptionResult> = _transcriptionResultFlow.asSharedFlow()

  private var isRunning = false
  private var pcmProcessingJob: Job? = null
  private var resultProcessingJob: Job? = null

  // 添加用户暂停状态管理
  @Volatile
  private var isPausedByUser = false // 用户主动暂停的标志

  // 移除外部缓存逻辑，现在由 OfflineEngine 内部处理

  init {
    // 设置引擎状态变化监听器
    offlineEngine.setStatusChangeListener { engineStatus ->
      isEngineInitialized = engineStatus.isInitialized && !engineStatus.isInitializing
      initializationInProgress = engineStatus.isInitializing

      AILog.d(TAG, "Engine status updated - initialized: $isEngineInitialized, initializing: $initializationInProgress")
    }
  }

  /**
   * 设置离线引擎配置
   */
  fun setConfig(modelPath: String): Boolean {
    try {
      if (initializationInProgress) {
        AILog.w(TAG, "Initialization already in progress")
        return false
      }

      if (isEngineInitialized) {
        AILog.w(TAG, "Engine already initialized")
        return true
      }

      offlineEngine.setModelPath(modelPath)
      val initStarted = offlineEngine.initialize()
      if (initStarted) {
        initializationInProgress = true
        AILog.i(TAG, "Engine initialization started, waiting for completion...")
      }
      return initStarted
    } catch (e: Exception) {
      AILog.e(TAG, "Error setting offline config", e)
      initializationInProgress = false
      return false
    }
  }

  /**
   * 开始离线转写处理
   * 支持异步初始化：OfflineEngine 内部会处理初始化期间的数据缓存
   */
  fun start(pcmDataFlow: SharedFlow<ByteArray>): Boolean {
    try {
      if (isRunning) {
        AILog.w(TAG, "Already running")
        return true
      }

      // 检查引擎状态
      if (!isEngineInitialized && !initializationInProgress) {
        AILog.e(TAG, "Cannot start: engine not initialized. Call setConfig() first")
        return false
      }

      // 启动引擎（如果还未启动）
      if (!offlineEngine.start()) {
        AILog.e(TAG, "Failed to start offline engine")
        return false
      }

      // 启动数据处理流程
      startDataProcessing(pcmDataFlow)

      isRunning = true
      AILog.i(TAG, "Offline orchestrator started successfully")
      return true

    } catch (e: Exception) {
      AILog.e(TAG, "Error starting offline orchestrator", e)
      return false
    }
  }

  /**
   * 停止离线转写处理
   */
  fun stop() {
    isRunning = false

    // 停止数据处理
    pcmProcessingJob?.cancel()
    resultProcessingJob?.cancel()

    // 停止离线引擎
    offlineEngine.stop()

    // 重置状态
    isPausedByUser = false

    AILog.i(TAG, "Offline orchestrator stopped")
  }

  /**
   * 释放资源
   */
  fun release() {
    stop()

    // 释放引擎资源
    offlineEngine.release()
    isEngineInitialized = false
    initializationInProgress = false
    shutdown("Orchestrator released")
    AILog.i(TAG, "Offline orchestrator released")
  }

  /**
   * 关闭协调器
   * 包括释放引擎资源和关闭协程作用域
   */
  fun shutdown(reason: String = "OfflineOrchestrator shutdown") {
    // 先释放引擎资源
    if (isEngineInitialized) {
      release()
    } else {
      stop() // 如果引擎未初始化，至少要停止处理
    }

    // 关闭协程作用域
    scopeDelegate.shutdown(reason)
    AILog.i(TAG, "Offline orchestrator shut down: $reason")
  }

  /**
   * 启动数据处理流程（引擎已初始化）
   */
  private fun startDataProcessing(pcmDataFlow: SharedFlow<ByteArray>) {
    // 启动PCM数据处理
    pcmProcessingJob = launch {
      pcmDataFlow.collect { pcmData ->
        try {
          if (!isPausedByUser) {
            offlineEngine.processPcmData(pcmData)
          }
        } catch (e: Exception) {
          AILog.e(TAG, "Error processing PCM data", e)
        }
      }
    }

    // 启动转写结果处理
    resultProcessingJob = launch {
      offlineEngine.transcriptionResultFlow.collect { offlineResult ->
        try {
          val transcriptionResult = UnifiedJson.decodeFromString<TranscriptionResult>(offlineResult)
          _transcriptionResultFlow.tryEmit(transcriptionResult)
        } catch (e: Exception) {
          AILog.e(TAG, "Error processing transcription result", e)
        }
      }
    }
  }

  /**
   * 暂停处理
   */
  fun pause() {
    isPausedByUser = true
    AILog.i(TAG, "Offline orchestrator paused")
  }

  /**
   * 恢复处理
   */
  fun resume() {
    isPausedByUser = false
    AILog.i(TAG, "Offline orchestrator resumed")
  }

  /**
   * 检查是否正在运行
   */
  fun isRunning(): Boolean = isRunning

  /**
   * 检查引擎是否已初始化
   */
  fun isEngineInitialized(): Boolean = isEngineInitialized

  /**
   * 检查是否正在初始化
   */
  fun isInitializing(): Boolean = initializationInProgress
}
