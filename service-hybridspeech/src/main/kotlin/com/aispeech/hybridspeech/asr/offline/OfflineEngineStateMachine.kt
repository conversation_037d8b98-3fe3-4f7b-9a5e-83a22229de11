package com.aispeech.hybridspeech.asr.offline

import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.BufferOverflow
import com.aispeech.aibase.AILog

/**
 * OfflineEngine 状态机
 * 统一管理引擎的所有状态和状态转换
 */
class OfflineEngineStateMachine {
    
    companion object {
        private const val TAG = "OfflineEngineStateMachine"
        private const val INIT_CACHE_CAPACITY = 1000
    }

    /**
     * 引擎状态枚举
     */
    sealed class EngineState {
        // 未初始化状态
        object Uninitialized : EngineState()
        
        // 初始化中状态（包含缓存子状态）
        data class Initializing(
            val isCaching: Boolean = false,
            val cacheSize: Int = 0
        ) : EngineState()
        
        // 已初始化但未启动
        object Initialized : EngineState()
        
        // 运行中状态（包含暂停子状态）
        data class Running(
            val isPaused: Boolean = false
        ) : EngineState()
        
        // 停止状态
        object Stopped : EngineState()
        
        // 错误状态
        data class Error(
            val errorMessage: String,
            val cause: Throwable? = null
        ) : EngineState()
        
        // 释放状态（终态）
        object Released : EngineState()
    }

    /**
     * 状态转换事件
     */
    sealed class StateEvent {
        object StartInitialization : StateEvent()
        object InitializationSuccess : StateEvent()
        data class InitializationFailed(val error: String, val cause: Throwable? = null) : StateEvent()
        object StartEngine : StateEvent()
        object StopEngine : StateEvent()
        object PauseEngine : StateEvent()
        object ResumeEngine : StateEvent()
        object StartCaching : StateEvent()
        object StopCaching : StateEvent()
        data class CacheDataReceived(val dataSize: Int) : StateEvent()
        data class ErrorOccurred(val error: String, val cause: Throwable? = null) : StateEvent()
        object ReleaseEngine : StateEvent()
    }

    // 当前状态
    private var _currentState: EngineState = EngineState.Uninitialized
    val currentState: EngineState get() = _currentState

    // 状态变化监听器
    private var stateChangeListener: ((EngineState, EngineState) -> Unit)? = null

    // 初始化缓存相关
    private val initCacheChannel = Channel<ByteArray>(
        capacity = INIT_CACHE_CAPACITY,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )
    private var cacheProcessingJob: Job? = null

    /**
     * 设置状态变化监听器
     */
    fun setStateChangeListener(listener: (oldState: EngineState, newState: EngineState) -> Unit) {
        stateChangeListener = listener
    }

    /**
     * 处理状态转换事件
     */
    fun handleEvent(event: StateEvent): Boolean {
        val oldState = _currentState
        val newState = when (oldState) {
            is EngineState.Uninitialized -> handleUninitializedState(event)
            is EngineState.Initializing -> handleInitializingState(oldState, event)
            is EngineState.Initialized -> handleInitializedState(event)
            is EngineState.Running -> handleRunningState(oldState, event)
            is EngineState.Stopped -> handleStoppedState(event)
            is EngineState.Error -> handleErrorState(oldState, event)
            is EngineState.Released -> handleReleasedState(event)
        }

        return if (newState != null && newState != oldState) {
            _currentState = newState
            AILog.d(TAG, "State transition: ${oldState::class.simpleName} -> ${newState::class.simpleName}")
            stateChangeListener?.invoke(oldState, newState)
            true
        } else {
            if (newState == null) {
                AILog.w(TAG, "Invalid state transition: ${oldState::class.simpleName} + ${event::class.simpleName}")
            }
            false
        }
    }

    /**
     * 处理未初始化状态的事件
     */
    private fun handleUninitializedState(event: StateEvent): EngineState? {
        return when (event) {
            is StateEvent.StartInitialization -> EngineState.Initializing()
            is StateEvent.ReleaseEngine -> EngineState.Released
            else -> null // 无效转换
        }
    }

    /**
     * 处理初始化中状态的事件
     */
    private fun handleInitializingState(currentState: EngineState.Initializing, event: StateEvent): EngineState? {
        return when (event) {
            is StateEvent.InitializationSuccess -> EngineState.Initialized
            is StateEvent.InitializationFailed -> EngineState.Error(event.error, event.cause)
            is StateEvent.StartCaching -> currentState.copy(isCaching = true)
            is StateEvent.StopCaching -> currentState.copy(isCaching = false, cacheSize = 0)
            is StateEvent.CacheDataReceived -> currentState.copy(cacheSize = currentState.cacheSize + event.dataSize)
            is StateEvent.ErrorOccurred -> EngineState.Error(event.error, event.cause)
            is StateEvent.ReleaseEngine -> EngineState.Released
            else -> null
        }
    }

    /**
     * 处理已初始化状态的事件
     */
    private fun handleInitializedState(event: StateEvent): EngineState? {
        return when (event) {
            is StateEvent.StartEngine -> EngineState.Running()
            is StateEvent.ErrorOccurred -> EngineState.Error(event.error, event.cause)
            is StateEvent.ReleaseEngine -> EngineState.Released
            else -> null
        }
    }

    /**
     * 处理运行中状态的事件
     */
    private fun handleRunningState(currentState: EngineState.Running, event: StateEvent): EngineState? {
        return when (event) {
            is StateEvent.StopEngine -> EngineState.Stopped
            is StateEvent.PauseEngine -> currentState.copy(isPaused = true)
            is StateEvent.ResumeEngine -> currentState.copy(isPaused = false)
            is StateEvent.ErrorOccurred -> EngineState.Error(event.error, event.cause)
            is StateEvent.ReleaseEngine -> EngineState.Released
            else -> null
        }
    }

    /**
     * 处理停止状态的事件
     */
    private fun handleStoppedState(event: StateEvent): EngineState? {
        return when (event) {
            is StateEvent.StartEngine -> EngineState.Running()
            is StateEvent.ErrorOccurred -> EngineState.Error(event.error, event.cause)
            is StateEvent.ReleaseEngine -> EngineState.Released
            else -> null
        }
    }

    /**
     * 处理错误状态的事件
     */
    private fun handleErrorState(currentState: EngineState.Error, event: StateEvent): EngineState? {
        return when (event) {
            is StateEvent.StartInitialization -> EngineState.Initializing() // 重新初始化
            is StateEvent.ReleaseEngine -> EngineState.Released
            else -> null
        }
    }

    /**
     * 处理释放状态的事件（终态，不允许转换）
     */
    private fun handleReleasedState(event: StateEvent): EngineState? {
        AILog.w(TAG, "Engine is released, ignoring event: ${event::class.simpleName}")
        return null
    }

    // 便捷方法：检查当前状态
    fun isUninitialized() = _currentState is EngineState.Uninitialized
    fun isInitializing() = _currentState is EngineState.Initializing
    fun isInitialized() = _currentState is EngineState.Initialized
    fun isRunning() = _currentState is EngineState.Running
    fun isStopped() = _currentState is EngineState.Stopped
    fun isError() = _currentState is EngineState.Error
    fun isReleased() = _currentState is EngineState.Released
    
    fun isPaused() = (_currentState as? EngineState.Running)?.isPaused == true
    fun isCaching() = (_currentState as? EngineState.Initializing)?.isCaching == true
    fun getCacheSize() = (_currentState as? EngineState.Initializing)?.cacheSize ?: 0

    /**
     * 获取初始化缓存通道
     */
    fun getInitCacheChannel() = initCacheChannel

    /**
     * 设置缓存处理任务
     */
    fun setCacheProcessingJob(job: Job?) {
        cacheProcessingJob = job
    }

    /**
     * 获取缓存处理任务
     */
    fun getCacheProcessingJob() = cacheProcessingJob

    /**
     * 清理资源
     */
    fun cleanup() {
        cacheProcessingJob?.cancel()
        initCacheChannel.close()
    }

    /**
     * 获取当前状态的描述信息
     */
    fun getStateDescription(): String {
        return when (val state = _currentState) {
            is EngineState.Uninitialized -> "未初始化"
            is EngineState.Initializing -> "初始化中${if (state.isCaching) " (缓存中: ${state.cacheSize})" else ""}"
            is EngineState.Initialized -> "已初始化"
            is EngineState.Running -> "运行中${if (state.isPaused) " (已暂停)" else ""}"
            is EngineState.Stopped -> "已停止"
            is EngineState.Error -> "错误: ${state.errorMessage}"
            is EngineState.Released -> "已释放"
        }
    }
}
