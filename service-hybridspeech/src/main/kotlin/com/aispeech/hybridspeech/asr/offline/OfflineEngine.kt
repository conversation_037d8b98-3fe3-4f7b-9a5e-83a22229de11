package com.aispeech.hybridspeech.asr.offline

import android.util.Log
import com.aispeech.AIError
import com.aispeech.AIResult
import com.aispeech.export.config.AILocalLASRConfig
import com.aispeech.export.config.MagnusRuntimeConfig
import com.aispeech.export.config.MagnusRuntimeEnvInfo
import com.aispeech.export.engines2.AILocalLASREngine
import com.aispeech.export.engines2.MagnusRuntimeHelper
import com.aispeech.export.intent.AILocalLASRIntent
import com.aispeech.export.listeners.AIASRListener
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import com.aispeech.lite.MagnusRuntimeTask
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeoutOrNull

/**
 * 离线转写引擎
 * 本地离线转写引擎，处理PCM数据并生成转写结果
 */
class OfflineEngine : CoroutineScope {
  companion object {
    private const val TAG = "OfflineEngine"
    private const val FRAME_SIZE_MS = 20 // 20ms处理一次，降低延迟
    private const val SAMPLE_RATE = 16000
    private const val BYTES_PER_SAMPLE = 2 // 16位PCM
    private const val FRAME_SIZE_BYTES = SAMPLE_RATE * BYTES_PER_SAMPLE * FRAME_SIZE_MS / 1000 // 640字节
    private const val SDK_ASSET_VERSION = 2
    private const val RING_BUFFER_SIZE = 64 * 1024 // 64KB环形缓冲区，可缓存约2秒音频
    private const val CHANNEL_CAPACITY = 50 // Channel容量
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  private val _transcriptionResultFlow = MutableSharedFlow<String>(
    replay = 0,
    extraBufferCapacity = 100
  )
  val transcriptionResultFlow: SharedFlow<String> = _transcriptionResultFlow.asSharedFlow()

  private var isRunning = false
  private var processingJob: Job? = null
  private val ringBuffer = RingBuffer(RING_BUFFER_SIZE)

  private lateinit var pcmChannel: Channel<ByteArray>

  // 添加暂停状态管理
  @Volatile
  private var isPaused = false

  // 初始化期间的缓存机制
  private val initCacheChannel = Channel<ByteArray>(
    capacity = 1000, // 缓存容量，可以缓存约30秒的音频数据
    onBufferOverflow = BufferOverflow.DROP_OLDEST
  )
  private var isInitCaching = false
  private var cacheProcessingJob: Job? = null

  private var modelPath: String = ""
  private var aiLocalLASREngine: AILocalLASREngine? = null
  private var isMagnusInitialized = false
  private var isEngineInitialized = false
  private var initializationInProgress = false

  // 状态变化监听器
  private var statusChangeListener: ((OfflineEngineStatus) -> Unit)? = null

  /**
   * 设置模型路径
   */
  fun setModelPath(path: String) {
    this.modelPath = path
    Log.i(TAG, "Model path set: $path")
  }

  /**
   * 设置状态变化监听器
   */
  fun setStatusChangeListener(listener: (OfflineEngineStatus) -> Unit) {
    this.statusChangeListener = listener
  }

  /**
   * 通知状态变化
   */
  private fun notifyStatusChange() {
    statusChangeListener?.invoke(getStatus())
  }

  /**
   * 初始化引擎
   */
  fun initialize(): Boolean {
    try {
      if (initializationInProgress) {
        Log.w(TAG, "Initialization already in progress")
        return false
      }

      if (isEngineInitialized) {
        Log.w(TAG, "Engine already initialized")
        return true
      }

      initializationInProgress = true
      isEngineInitialized = false

      // 通知开始初始化
      notifyStatusChange()

      // 使用默认路径如果没有设置
      if (modelPath.isEmpty()) {
        modelPath = getDefaultModelPath()
        Log.i(TAG, "Using default model path: $modelPath")
      }

      // 初始化Magnus运行时环境
      if (!initializeMagnusRuntime()) {
        Log.e(TAG, "Failed to initialize Magnus runtime")
        initializationInProgress = false
        notifyStatusChange()
        return false
      }

      // 创建AILocalLASREngine实例
      aiLocalLASREngine = AILocalLASREngine.createInstance()
      if (aiLocalLASREngine == null) {
        Log.e(TAG, "Failed to create AILocalLASREngine instance")
        initializationInProgress = false
        notifyStatusChange()
        return false
      }

      // 配置LASR引擎
      val lasrConfig = AILocalLASRConfig.Builder()
        .setTaskName(MagnusRuntimeTask.MODEL_LASR)
        .build()

      // 初始化引擎 - 这里会触发onInit回调
      aiLocalLASREngine!!.init(lasrConfig, createAsrListener())

      Log.i(TAG, "Offline engine initialization started, waiting for onInit callback...")
      return true

    } catch (e: Exception) {
      Log.e(TAG, "Error initializing offline engine", e)
      initializationInProgress = false
      return false
    }
  }

  /**
   * 获取默认模型路径
   */
  private fun getDefaultModelPath(): String {
    return "/sdcard/hybrid_speech_debug/offline_model"
  }

  /**
   * 开始处理
   */
  fun start(): Boolean {
    try {
      if (isRunning) {
        Log.w(TAG, "Already running")
        return true
      }

      if (aiLocalLASREngine == null || !isEngineInitialized) {
        Log.e(TAG, "Engine not properly initialized")
        return false
      }

      if (initializationInProgress) {
        Log.e(TAG, "Engine initialization still in progress")
        return false
      }

      // 为新的处理会话创建一个新的 Channel
      pcmChannel = Channel(
        capacity = CHANNEL_CAPACITY,
        onBufferOverflow = BufferOverflow.DROP_LATEST
      )

      val param = AILocalLASRIntent().apply {
        isUseCustomFeed = true
        isUseTxtSmooth = true
        isUseWpInRec = true
        isUseSensitiveWdsNorm = true
        isUseStreamChar = false
        isUseTprocess = true
        isUsePhrase = true
      }
      aiLocalLASREngine!!.start(param)

      isRunning = true

      processingJob = launch {
        processingLoop()
      }

      Log.i(TAG, "Offline engine started")
      return true

    } catch (e: Exception) {
      Log.e(TAG, "Error starting offline engine", e)
      return false
    }
  }

  /**
   * 停止处理
   */
  fun stop(timeoutMs: Long = 500) {
    if (!isRunning) {
      Log.w(TAG, "Engine not running, stop command ignored.")
      return
    }

    Log.i(TAG, "Stopping offline engine...")

    isRunning = false
    isPaused = false

    try {
      if (::pcmChannel.isInitialized && !pcmChannel.isClosedForSend) {
        pcmChannel.close()
        Log.d(TAG, "PCM channel closed.")
      }
    } catch (e: Exception) {
      Log.w(TAG, "Exception while closing channel, might be already closed.", e)
    }

    runBlocking {
      try {
        withTimeoutOrNull(timeoutMs) {
          processingJob?.join()
          Log.d(TAG, "Processing job finished joining.")
        }
      } catch (e: Exception) {
        Log.e(TAG, "Error waiting for processing job to join", e)
      }
    }

    flushRemainingAudio()
    Log.d(TAG, "Flushed remaining audio from RingBuffer.")

    aiLocalLASREngine?.stop()
    Log.d(TAG, "Called engine.stop().")

    processingJob = null
    ringBuffer.clear()

    // 清理初始化缓存
    clearInitCache()

    Log.i(TAG, "Offline engine stopped successfully.")
  }

  /**
   * 关闭引擎协程作用域
   * 取消所有子协程，但不影响其他模块
   */
  fun shutdown(reason: String = "OfflineEngine shutdown") {
    scopeDelegate.shutdown(reason)
    Log.i(TAG, "Offline engine scope shut down: $reason")
  }

  /**
   * 释放资源
   */
  fun release() {
    stop()
    aiLocalLASREngine?.destroy()
    aiLocalLASREngine = null
    isEngineInitialized = false
    initializationInProgress = false

    // 关闭初始化缓存通道
    initCacheChannel.close()

    // 关闭协程作用域
    shutdown("Engine released")
    Log.i(TAG, "Offline engine released")
  }

  /**
   * 暂停处理（清空缓存但保留引擎状态）
   */
  fun pause() {
    if (!isRunning) {
      Log.w(TAG, "Cannot pause: not running")
      return
    }

    if (isPaused) {
      Log.w(TAG, "Already paused")
      return
    }

    Log.i(TAG, "Pausing offline engine processing")

    try {
      isPaused = true

      // 停止处理循环
      processingJob?.cancel()
      processingJob = null

      // 清空缓存数据，释放内存
      clearBuffers()

      Log.i(TAG, "Offline engine paused successfully")
    } catch (e: Exception) {
      Log.e(TAG, "Error pausing offline engine", e)
    }
  }

  /**
   * 恢复处理
   */
  fun resume() {
    if (!isRunning) {
      Log.w(TAG, "Cannot resume: not running")
      return
    }

    if (!isPaused) {
      Log.w(TAG, "Not paused, no need to resume")
      return
    }

    Log.i(TAG, "Resuming offline engine processing")

    try {
      isPaused = false

      // 重新创建Channel，因为暂停时可能已经关闭
      pcmChannel = Channel(
        capacity = CHANNEL_CAPACITY,
        onBufferOverflow = BufferOverflow.DROP_LATEST
      )

      // 重新启动处理循环
      processingJob = launch {
        processingLoop()
      }

      Log.i(TAG, "Offline engine resumed successfully")
    } catch (e: Exception) {
      Log.e(TAG, "Error resuming offline engine", e)
    }
  }

  /**
   * 清空所有缓存数据
   */
  private fun clearBuffers() {
    try {
      // 清空环形缓冲区
      ringBuffer.clear()

      Log.d(TAG, "Cleared all buffers")
    } catch (e: Exception) {
      Log.e(TAG, "Error clearing buffers", e)
    }
  }

  /**
   * 处理初始化期间缓存的数据
   */
  private fun processCachedData() {
    if (!isInitCaching) {
      return
    }

    Log.i(TAG, "Processing cached PCM data after initialization")

    cacheProcessingJob = launch {
      try {
        var processedCount = 0

        // 处理缓存的数据
        while (true) {
          val cachedData = initCacheChannel.tryReceive().getOrNull()
          if (cachedData == null) {
            break
          }

          // 如果引擎正在运行，直接发送到处理通道
          if (isRunning && !isPaused && ::pcmChannel.isInitialized) {
            pcmChannel.trySend(cachedData)
          }
          processedCount++
        }

        Log.i(TAG, "Processed $processedCount cached PCM frames")
        isInitCaching = false

      } catch (e: Exception) {
        Log.e(TAG, "Error processing cached PCM data", e)
      }
    }
  }

  /**
   * 清理初始化缓存
   */
  private fun clearInitCache() {
    try {
      var clearedCount = 0
      while (true) {
        val data = initCacheChannel.tryReceive().getOrNull()
        if (data == null) {
          break
        }
        clearedCount++
      }

      if (clearedCount > 0) {
        Log.i(TAG, "Cleared $clearedCount cached PCM frames")
      }

      isInitCaching = false
      cacheProcessingJob?.cancel()
      cacheProcessingJob = null

    } catch (e: Exception) {
      Log.e(TAG, "Error clearing init cache", e)
    }
  }

  /**
   * 处理PCM数据
   * 支持初始化期间的数据缓存
   */
  fun processPcmData(pcmData: ByteArray) {
    when {
      initializationInProgress -> {
        // 初始化期间缓存数据
        if (!initCacheChannel.trySend(pcmData.copyOf()).isSuccess) {
          Log.w(TAG, "Init cache channel full, dropping oldest data")
        }
        if (!isInitCaching) {
          isInitCaching = true
          Log.i(TAG, "Started caching PCM data during initialization")
        }
      }
      isRunning && !isPaused && ::pcmChannel.isInitialized -> {
        // 正常处理模式
        pcmChannel.trySend(pcmData)
      }
      else -> {
        // 引擎未运行或已暂停，丢弃数据
        Log.d(TAG, "Engine not ready, dropping PCM data")
      }
    }
  }

  /**
   * 处理循环
   */
  private suspend fun processingLoop() {
    val frame = ByteArray(FRAME_SIZE_BYTES)          // 20ms → 640字节
    try {
      for (data in pcmChannel) {                     // 挂起式消费
        ringBuffer.write(data)
        while (ringBuffer.read(frame)) {
          aiLocalLASREngine?.feedData(frame, frame.size)
        }
      }
    } catch (e: Exception) {
      Log.e(TAG, "processingLoop error", e)
    } finally {
      if (!isPaused) {
        isRunning = false
      }
    }
  }

  /**
   * 刷新剩余音频数据
   */
  private fun flushRemainingAudio() {
    try {
      val remainingSize = ringBuffer.size()
      if (remainingSize > 0) {
        val lastBytes = ByteArray(remainingSize)
        if (ringBuffer.read(lastBytes)) {
          aiLocalLASREngine?.feedData(lastBytes, lastBytes.size)
        }
      }
    } catch (e: Exception) {
      Log.e(TAG, "Error flushing remaining audio", e)
    }
  }

  /**
   * 获取引擎状态
   */
  fun getStatus(): OfflineEngineStatus {
    // 获取初始化缓存大小
    val initCacheSize = try {
      var count = 0
      while (true) {
        val data = initCacheChannel.tryReceive().getOrNull()
        if (data == null) break
        // 重新放回数据
        initCacheChannel.trySend(data)
        count++
      }
      count
    } catch (e: Exception) {
      0
    }

    return OfflineEngineStatus(
      isRunning = isRunning,
      isInitialized = isEngineInitialized,
      isInitializing = initializationInProgress,
      isPaused = isPaused,
      modelPath = modelPath,
      bufferSize = ringBuffer.size(),
      queueSize = if (::pcmChannel.isInitialized) {
        pcmChannel.tryReceive().getOrNull()?.let {
          pcmChannel.trySend(it)
          1
        } ?: 0
      } else {
        0
      },
      isInitCaching = isInitCaching,
      initCacheSize = initCacheSize
    )
  }

  /**
   * 初始化Magnus运行时环境
   */
  private fun initializeMagnusRuntime(): Boolean {
    try {
      if (isMagnusInitialized) {
        Log.i(TAG, "Magnus runtime already initialized")
        return true
      }

      val magnusRuntimeEnvInfo = MagnusRuntimeEnvInfo.Builder()
        .setType(MagnusRuntimeEnvInfo.TYPE_CLIENT)
        .setLogLevel(Log.WARN)
        .create()
//
//      val assetFolderPath = "magnus_with_translate"
//      val targetSubdirectoryName = "magnusRes"
//
//      val copiedConfigPath = AssetCopyUtil.copyAssetsToInternalStorage(
//        context,
//        assetFolderPath,
//        targetSubdirectoryName,
//        SDK_ASSET_VERSION
//      )

      MagnusRuntimeHelper.getInstance().init(
        MagnusRuntimeConfig.Builder()
          .setPath(modelPath)
          .setInfo(magnusRuntimeEnvInfo.toJson())
          .setServerType(true)
          .create()
      )

      isMagnusInitialized = true
      Log.i(TAG, "Magnus runtime initialized successfully")
      return true

    } catch (e: Exception) {
      Log.e(TAG, "Error initializing Magnus runtime", e)
      return false
    }
  }

  /**
   * 创建ASR监听器
   */
  private fun createAsrListener(): AIASRListener {
    return object : AIASRListener {
      override fun onInit(p0: Int) {
        Log.d(TAG, "onInit status: $p0")
        initializationInProgress = false
        if (p0 == 0) {
          isEngineInitialized = true
          Log.i(TAG, "Engine initialization completed successfully")

          // 处理初始化期间缓存的数据
          if (isInitCaching) {
            processCachedData()
          }
        } else {
          isEngineInitialized = false
          Log.e(TAG, "Engine initialization failed with status: $p0")

          // 初始化失败，清理缓存数据
          if (isInitCaching) {
            clearInitCache()
          }
        }

        // 通知状态变化
        notifyStatusChange()
      }

      override fun onError(p0: AIError?) {
        Log.d(TAG, "onError error: $p0")
      }

      override fun onReadyForSpeech() {
        Log.d(TAG, "onReadyForSpeech")
      }

      override fun onResultDataReceived(p0: ByteArray?, p1: Int) {
        // 不需要处理
      }

      override fun onResultDataReceived(p0: ByteArray?, p1: Int, p2: Int) {
        // 不需要处理
      }

      override fun onRawDataReceived(p0: ByteArray?, p1: Int) {
        // 不需要处理
      }

      override fun onResults(result: AIResult?) {
        Log.d(TAG, "onResults: $result")
        launch {
          _transcriptionResultFlow.emit(result?.resultJSONObject.toString())
        }
      }

      override fun onRmsChanged(p0: Float) {
        // 音量变化，可以根据需要处理
      }

      override fun onBeginningOfSpeech() {
        Log.d(TAG, "onBeginningOfSpeech")
      }

      override fun onEndOfSpeech() {
        Log.d(TAG, "onEndOfSpeech")
      }

      override fun onNotOneShot() {
        Log.d(TAG, "onNotOneShot")
      }

    }
  }

}

/**
 * 使用固定长度的环形缓冲区，线程安全且零拷贝
 */
private class RingBuffer(capacity: Int) {
  private val buf = ByteArray(capacity)
  private var readPos = 0
  private var writePos = 0
  private var available = 0
  private val lock = Any()

  private fun discard(count: Int) {
    val real = minOf(count, available)
    readPos = (readPos + real) % buf.size
    available -= real
  }

  fun write(src: ByteArray) = synchronized(lock) {
    if (src.size > buf.size - available) {
      discard(src.size - (buf.size - available))
    }
    var offset = 0
    var len = src.size
    while (len > 0) {
      val space = minOf(len, buf.size - writePos)
      System.arraycopy(src, offset, buf, writePos, space)
      writePos = (writePos + space) % buf.size
      offset += space
      len -= space
      available += space
    }
  }

  fun read(dst: ByteArray): Boolean {
    synchronized(lock) {
      if (available < dst.size) return false
      var offset = 0
      var len = dst.size
      while (len > 0) {
        val chunk = minOf(len, buf.size - readPos)
        System.arraycopy(buf, readPos, dst, offset, chunk)
        readPos = (readPos + chunk) % buf.size
        offset += chunk
        len -= chunk
        available -= chunk
      }
      return true
    }
  }

  fun size(): Int = synchronized(lock) { available }

  // 添加清空缓冲区的方法
  fun clear() = synchronized(lock) {
    readPos = 0
    writePos = 0
    available = 0
  }
}

/**
 * 离线引擎状态
 */
data class OfflineEngineStatus(
  val isRunning: Boolean,
  val isInitialized: Boolean,
  val isInitializing: Boolean,
  val isPaused: Boolean,
  val modelPath: String,
  val bufferSize: Int,
  val queueSize: Int,
  val isInitCaching: Boolean,
  val initCacheSize: Int
)
